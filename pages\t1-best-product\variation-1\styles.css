/* T1 Best Product Page - Variation 1: Modern Card-Based Layout */

/* Header */
.header {
  background: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
}

.nav__logo {
  height: 40px;
  width: auto;
}

.nav__menu {
  display: flex;
  gap: var(--space-8);
}

.nav__link {
  color: var(--color-gray-700);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

.nav__link:hover {
  color: var(--petful-primary);
}

.nav__cta {
  background: var(--petful-primary);
  color: var(--color-white);
  border: none;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-fast);
}

.nav__cta:hover {
  background: var(--petful-primary-dark);
}

/* Trust Banner */
.trust-banner {
  background: linear-gradient(135deg, var(--petful-primary), var(--petful-primary-light));
  color: var(--color-white);
  padding: var(--space-4) 0;
}

.trust-banner__content {
  display: flex;
  align-items: center;
  gap: var(--space-6);
  flex-wrap: wrap;
}

.trust-banner__badge {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: rgba(255, 255, 255, 0.2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
}

.trust-banner__number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
}

.trust-banner__text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.trust-banner__disclaimer {
  flex: 1;
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.trust-banner__trust {
  font-size: var(--font-size-sm);
  background: rgba(255, 255, 255, 0.2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
}

/* Breadcrumb */
.breadcrumb {
  background: var(--color-gray-50);
  padding: var(--space-3) 0;
}

.breadcrumb__list {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  list-style: none;
  margin: 0;
  padding: 0;
  font-size: var(--font-size-sm);
}

.breadcrumb__item {
  color: var(--color-gray-600);
}

.breadcrumb__item:not(:last-child)::after {
  content: "›";
  margin-left: var(--space-2);
  color: var(--color-gray-400);
}

.breadcrumb__item a {
  color: var(--petful-primary);
  text-decoration: none;
}

.breadcrumb__item a:hover {
  text-decoration: underline;
}

/* Main Content */
.main {
  padding: var(--space-8) 0;
}

/* Hero Section */
.hero {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-8);
  align-items: center;
  margin-bottom: var(--space-12);
}

.hero__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-4) 0;
  line-height: var(--line-height-tight);
}

.hero__subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  margin: 0 0 var(--space-4) 0;
  line-height: var(--line-height-relaxed);
}

.hero__meta {
  display: flex;
  gap: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
}

.hero__image {
  position: relative;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.hero__img {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

/* Quick Navigation */
.quick-nav {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  background: var(--color-white);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-base);
  margin-bottom: var(--space-8);
  border: 1px solid var(--color-gray-200);
}

.quick-nav__title {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  white-space: nowrap;
}

.quick-nav__links {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.quick-nav__link {
  color: var(--petful-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.quick-nav__link:hover {
  background: var(--color-gray-100);
}

/* Introduction */
.intro {
  margin-bottom: var(--space-12);
}

.intro__content {
  max-width: 800px;
}

.intro__text {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--color-gray-700);
  margin-bottom: var(--space-4);
}

.intro__text:last-child {
  margin-bottom: 0;
}

/* Section Titles */
.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-8) 0;
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-3);
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--petful-primary);
  border-radius: var(--radius-full);
}

/* Top Picks */
.top-picks {
  margin-bottom: var(--space-16);
}

.top-picks .product-card {
  margin-bottom: var(--space-8);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Comparison Table */
.comparison-table {
  margin-bottom: var(--space-16);
}

.table-container {
  overflow-x: auto;
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-base);
  border: 1px solid var(--color-gray-200);
}

.comparison-table__table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.comparison-table__table th {
  background: var(--color-gray-50);
  padding: var(--space-4);
  text-align: left;
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  border-bottom: 1px solid var(--color-gray-200);
}

.comparison-table__table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--color-gray-100);
  vertical-align: middle;
}

.comparison-table__row--featured {
  background: rgba(255, 107, 53, 0.05);
}

.comparison-table__product {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  min-width: 200px;
}

.comparison-table__image {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: var(--radius-sm);
}

.comparison-table__subtitle {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

.comparison-table__score {
  text-align: center;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  transition: var(--transition-fast);
  cursor: pointer;
  border: none;
}

.btn--primary {
  background: var(--petful-primary);
  color: var(--color-white);
}

.btn--primary:hover {
  background: var(--petful-primary-dark);
  transform: translateY(-1px);
}

.btn--sm {
  padding: var(--space-1) var(--space-3);
  font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .hero__image {
    order: -1;
  }
}

@media (max-width: 768px) {
  .nav__menu {
    display: none;
  }
  
  .trust-banner__content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }
  
  .hero__title {
    font-size: var(--font-size-3xl);
  }
  
  .quick-nav {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .quick-nav__links {
    width: 100%;
    justify-content: center;
  }
  
  .comparison-table__table {
    font-size: var(--font-size-xs);
  }
  
  .comparison-table__table th,
  .comparison-table__table td {
    padding: var(--space-2);
  }
}

@media (max-width: 480px) {
  .main {
    padding: var(--space-4) 0;
  }
  
  .hero__title {
    font-size: var(--font-size-2xl);
  }
  
  .section-title {
    font-size: var(--font-size-2xl);
  }
}

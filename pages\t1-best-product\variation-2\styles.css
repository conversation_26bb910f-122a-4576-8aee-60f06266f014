/* T1 Best Product Page - Variation 2: Results-Focused Layout */

/* Header */
.header {
  background: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
}

.nav__logo {
  height: 40px;
  width: auto;
}

.nav__menu {
  display: flex;
  gap: var(--space-8);
}

.nav__link {
  color: var(--color-gray-700);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

.nav__link:hover {
  color: var(--petful-primary);
}

.nav__cta {
  background: var(--petful-primary);
  color: var(--color-white);
  border: none;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-fast);
}

.nav__cta:hover {
  background: var(--petful-primary-dark);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 100%);
  padding: var(--space-16) 0;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(45deg, transparent 0%, rgba(255, 107, 53, 0.05) 100%);
  pointer-events: none;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
  align-items: center;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--petful-primary);
  color: var(--color-white);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-4);
}

.hero-badge__icon {
  font-size: var(--font-size-lg);
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-4) 0;
  line-height: var(--line-height-tight);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
  margin: 0 0 var(--space-6) 0;
  line-height: var(--line-height-relaxed);
}

.hero-trust {
  display: flex;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.trust-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.trust-item__icon {
  color: var(--color-success);
  font-weight: var(--font-weight-bold);
}

.trust-item__text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

.hero-cta {
  display: flex;
  gap: var(--space-4);
}

.hero-visual {
  position: relative;
}

.hero-image {
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  position: relative;
}

.hero-img {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.hero-stats {
  position: absolute;
  bottom: -var(--space-6);
  left: var(--space-4);
  right: var(--space-4);
  display: flex;
  gap: var(--space-3);
}

.stat-card {
  background: var(--color-white);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  text-align: center;
  flex: 1;
}

.stat-card__number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--petful-primary);
  margin-bottom: var(--space-1);
}

.stat-card__label {
  font-size: var(--font-size-xs);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Results Summary */
.results-summary {
  padding: var(--space-16) 0;
  background: var(--color-white);
}

.results-summary__title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  text-align: center;
  margin: 0 0 var(--space-8) 0;
  color: var(--color-gray-900);
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.result-card {
  background: var(--color-white);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  text-align: center;
  transition: var(--transition-base);
  position: relative;
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.result-card--winner {
  border-color: var(--petful-primary);
  background: linear-gradient(135deg, var(--color-white) 0%, rgba(255, 107, 53, 0.02) 100%);
}

.result-card__badge {
  background: var(--petful-primary);
  color: var(--color-white);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-4);
  display: inline-block;
}

.result-card__product {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.result-card__image {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: var(--radius-md);
}

.result-card__info {
  text-align: left;
  flex: 1;
}

.result-card__name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-1) 0;
}

.result-card__score {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--petful-primary);
}

.result-card__reason {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  font-style: italic;
}

/* Detailed Reviews */
.detailed-reviews {
  padding: var(--space-16) 0;
  background: var(--color-gray-50);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  text-align: center;
  margin: 0 0 var(--space-12) 0;
  color: var(--color-gray-900);
}

.review-card {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  margin-bottom: var(--space-8);
}

.review-card--featured {
  border: 2px solid var(--petful-primary);
}

.review-card__header {
  background: var(--color-gray-50);
  padding: var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.review-card__badge {
  background: var(--petful-primary);
  color: var(--color-white);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
}

.review-card__meta {
  display: flex;
  gap: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
}

.review-card__content {
  padding: var(--space-6);
}

.review-card__product {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.review-card__image-container {
  background: var(--color-gray-50);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  text-align: center;
}

.review-card__image {
  width: 100%;
  height: 150px;
  object-fit: contain;
}

.review-card__title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-3) 0;
}

.review-card__specs {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.spec-tag {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.review-card__score-display {
  display: flex;
  gap: var(--space-4);
  align-items: center;
}

.score-circle {
  position: relative;
  width: 80px;
  height: 80px;
}

.score-circle__ring {
  transform: rotate(-90deg);
}

.score-circle__text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--petful-primary);
}

.score-breakdown {
  flex: 1;
}

.score-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-2);
}

.score-item__label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  min-width: 80px;
}

.score-item__bar {
  flex: 1;
  height: 6px;
  background: var(--color-gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.score-item__fill {
  height: 100%;
  background: var(--petful-primary);
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
}

.score-item__value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--petful-primary);
  min-width: 30px;
  text-align: right;
}

.review-card__summary-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-3) 0;
}

.review-card__summary-text {
  font-size: var(--font-size-base);
  color: var(--color-gray-700);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-4);
}

.review-card__highlights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-2);
  margin-bottom: var(--space-6);
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-sm);
}

.highlight-item--pro .highlight-item__icon {
  color: var(--color-success);
}

.highlight-item--con .highlight-item__icon {
  color: var(--color-warning);
}

.review-card__actions {
  display: flex;
  gap: var(--space-3);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  transition: var(--transition-fast);
  cursor: pointer;
  border: 2px solid transparent;
  font-size: var(--font-size-base);
}

.btn--large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
}

.btn--sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
}

.btn--primary {
  background: var(--petful-primary);
  color: var(--color-white);
}

.btn--primary:hover {
  background: var(--petful-primary-dark);
  transform: translateY(-1px);
}

.btn--secondary {
  background: var(--color-white);
  color: var(--petful-primary);
  border-color: var(--petful-primary);
}

.btn--secondary:hover {
  background: var(--petful-primary);
  color: var(--color-white);
}

.btn--outline {
  background: transparent;
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
}

.btn--outline:hover {
  background: var(--color-gray-100);
  border-color: var(--color-gray-400);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }
  
  .hero-visual {
    order: -1;
  }
  
  .review-card__product {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .review-card__score-display {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .nav__menu {
    display: none;
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .hero-trust {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .hero-cta {
    flex-direction: column;
  }
  
  .hero-stats {
    position: static;
    margin-top: var(--space-4);
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .review-card__actions {
    flex-direction: column;
  }
}

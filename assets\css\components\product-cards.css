/* Product Card Components */

/* Base Product Card */
.product-card {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  transition: var(--transition-base);
  border: 1px solid var(--color-gray-200);
  position: relative;
}

.product-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.product-card__badge {
  position: absolute;
  top: var(--space-3);
  left: var(--space-3);
  background: var(--petful-primary);
  color: var(--color-white);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  z-index: 10;
}

.product-card__badge--best-value {
  background: var(--color-success);
}

.product-card__badge--best-for-seniors {
  background: var(--petful-accent);
}

.product-card__badge--glucosamine-free {
  background: var(--color-warning);
}

/* Product Image */
.product-card__image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: var(--color-gray-50);
}

.product-card__image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: var(--transition-base);
}

.product-card:hover .product-card__image {
  transform: scale(1.05);
}

/* Product Content */
.product-card__content {
  padding: var(--space-6);
}

.product-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
  line-height: var(--line-height-tight);
}

.product-card__title a {
  color: inherit;
  text-decoration: none;
  transition: var(--transition-fast);
}

.product-card__title a:hover {
  color: var(--petful-primary);
}

.product-card__subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin: 0 0 var(--space-4) 0;
}

/* Rating Display */
.product-card__rating {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.product-card__score {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.product-card__score-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--petful-primary);
}

.product-card__score-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
}

.product-card__rating-bar {
  flex: 1;
  height: 8px;
  background: var(--color-gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.product-card__rating-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--petful-primary), var(--petful-primary-light));
  border-radius: var(--radius-full);
  transition: width var(--transition-base);
}

/* Key Features */
.product-card__features {
  margin-bottom: var(--space-4);
}

.product-card__features-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  margin: 0 0 var(--space-2) 0;
}

.product-card__features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.product-card__features-item {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin-bottom: var(--space-1);
  position: relative;
  padding-left: var(--space-4);
}

.product-card__features-item::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--color-success);
  font-weight: var(--font-weight-bold);
}

/* Pros and Cons */
.product-card__pros-cons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.product-card__pros,
.product-card__cons {
  font-size: var(--font-size-sm);
}

.product-card__pros-title,
.product-card__cons-title {
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--space-2) 0;
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.product-card__pros-title {
  color: var(--color-success);
}

.product-card__cons-title {
  color: var(--color-error);
}

.product-card__pros-list,
.product-card__cons-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.product-card__pros-item,
.product-card__cons-item {
  margin-bottom: var(--space-1);
  color: var(--color-gray-600);
}

/* Action Buttons */
.product-card__actions {
  display: flex;
  gap: var(--space-3);
  margin-top: var(--space-6);
}

.product-card__cta {
  flex: 1;
  background: var(--petful-primary);
  color: var(--color-white);
  border: none;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  text-align: center;
  transition: var(--transition-fast);
  cursor: pointer;
}

.product-card__cta:hover {
  background: var(--petful-primary-dark);
  transform: translateY(-1px);
}

.product-card__secondary-cta {
  background: var(--color-white);
  color: var(--petful-primary);
  border: 2px solid var(--petful-primary);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  text-align: center;
  transition: var(--transition-fast);
  cursor: pointer;
}

.product-card__secondary-cta:hover {
  background: var(--petful-primary);
  color: var(--color-white);
}

/* Compact Card Variation */
.product-card--compact {
  display: flex;
  align-items: center;
  padding: var(--space-4);
}

.product-card--compact .product-card__image-container {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
  margin-right: var(--space-4);
}

.product-card--compact .product-card__content {
  padding: 0;
  flex: 1;
}

.product-card--compact .product-card__title {
  font-size: var(--font-size-base);
  margin-bottom: var(--space-1);
}

.product-card--compact .product-card__rating {
  margin-bottom: var(--space-2);
}

.product-card--compact .product-card__actions {
  margin-top: var(--space-3);
}

/* Featured Card Variation */
.product-card--featured {
  border: 2px solid var(--petful-primary);
  position: relative;
}

.product-card--featured::before {
  content: "Editor's Choice";
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--petful-primary);
  color: var(--color-white);
  padding: var(--space-1) var(--space-4);
  border-radius: 0 0 var(--radius-md) var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-card__pros-cons {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .product-card__actions {
    flex-direction: column;
  }
  
  .product-card--compact {
    flex-direction: column;
    text-align: center;
  }
  
  .product-card--compact .product-card__image-container {
    width: 100%;
    height: 150px;
    margin-right: 0;
    margin-bottom: var(--space-3);
  }
}

/* Loading State */
.product-card--loading {
  pointer-events: none;
}

.product-card--loading .product-card__image {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Rating System Components */

/* Overall Score Display */
.overall-score {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-6);
  background: linear-gradient(135deg, var(--color-gray-50), var(--color-white));
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
  margin-bottom: var(--space-6);
}

.overall-score__number {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--petful-primary);
  line-height: 1;
}

.overall-score__details {
  flex: 1;
}

.overall-score__label {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-1) 0;
}

.overall-score__subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin: 0;
}

.overall-score__visual {
  width: 120px;
  height: 120px;
  position: relative;
}

/* Circular Progress Ring */
.score-ring {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.score-ring__background {
  fill: none;
  stroke: var(--color-gray-200);
  stroke-width: 8;
}

.score-ring__progress {
  fill: none;
  stroke: var(--petful-primary);
  stroke-width: 8;
  stroke-linecap: round;
  stroke-dasharray: 283; /* 2 * π * 45 (radius) */
  stroke-dashoffset: 283;
  transition: stroke-dashoffset var(--transition-slow);
}

.score-ring__text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(90deg);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--petful-primary);
}

/* Category Scores */
.category-scores {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.category-score {
  background: var(--color-white);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-gray-200);
  transition: var(--transition-base);
}

.category-score:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--petful-primary);
}

.category-score__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
}

.category-score__name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0;
}

.category-score__value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--petful-primary);
}

.category-score__bar {
  width: 100%;
  height: 8px;
  background: var(--color-gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-2);
}

.category-score__fill {
  height: 100%;
  background: linear-gradient(90deg, var(--petful-primary), var(--petful-primary-light));
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
  position: relative;
}

.category-score__fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--color-white);
  border-radius: var(--radius-full);
  opacity: 0.8;
}

.category-score__description {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
}

/* Star Rating */
.star-rating {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.star-rating__star {
  width: 20px;
  height: 20px;
  color: var(--color-gray-300);
  transition: var(--transition-fast);
}

.star-rating__star--filled {
  color: var(--petful-primary);
}

.star-rating__star--half {
  background: linear-gradient(90deg, var(--petful-primary) 50%, var(--color-gray-300) 50%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.star-rating__count {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin-left: var(--space-2);
}

/* Comparison Scores */
.comparison-scores {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.comparison-score {
  text-align: center;
  padding: var(--space-4);
  background: var(--color-white);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-gray-200);
}

.comparison-score__label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin: 0 0 var(--space-2) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: var(--font-weight-medium);
}

.comparison-score__value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--petful-primary);
  margin: 0 0 var(--space-1) 0;
}

.comparison-score__context {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  margin: 0;
}

/* Score Badges */
.score-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.score-badge--excellent {
  background: var(--color-success);
  color: var(--color-white);
}

.score-badge--good {
  background: var(--petful-primary);
  color: var(--color-white);
}

.score-badge--average {
  background: var(--color-warning);
  color: var(--color-white);
}

.score-badge--poor {
  background: var(--color-error);
  color: var(--color-white);
}

/* Interactive Rating */
.interactive-rating {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: var(--space-4) 0;
}

.interactive-rating__label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-right: var(--space-2);
}

.interactive-rating__stars {
  display: flex;
  gap: var(--space-1);
}

.interactive-rating__star {
  width: 24px;
  height: 24px;
  color: var(--color-gray-300);
  cursor: pointer;
  transition: var(--transition-fast);
}

.interactive-rating__star:hover,
.interactive-rating__star--active {
  color: var(--petful-primary);
  transform: scale(1.1);
}

/* Rating Summary */
.rating-summary {
  background: var(--color-gray-50);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  margin: var(--space-6) 0;
}

.rating-summary__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-4) 0;
  text-align: center;
}

.rating-summary__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.rating-summary__item {
  text-align: center;
}

.rating-summary__item-score {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--petful-primary);
  margin: 0 0 var(--space-1) 0;
}

.rating-summary__item-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .overall-score {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }
  
  .overall-score__visual {
    width: 100px;
    height: 100px;
  }
  
  .category-scores {
    grid-template-columns: 1fr;
  }
  
  .comparison-scores {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .rating-summary__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .comparison-scores {
    grid-template-columns: 1fr;
  }
  
  .rating-summary__grid {
    grid-template-columns: 1fr;
  }
}

/* Animation for score reveals */
@keyframes scoreReveal {
  from {
    stroke-dashoffset: 283;
  }
  to {
    stroke-dashoffset: var(--final-offset);
  }
}

.score-ring__progress--animate {
  animation: scoreReveal var(--transition-slow) ease-out forwards;
}

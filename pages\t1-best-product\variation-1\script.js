// T1 Best Product Page - Variation 1 JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all interactive features
    initSmoothScrolling();
    initRatingAnimations();
    initProductCardInteractions();
    initTableSorting();
    initLazyLoading();
    initAnalytics();
});

// Smooth scrolling for navigation links
function initSmoothScrolling() {
    const quickNavLinks = document.querySelectorAll('.quick-nav__link');
    
    quickNavLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // Update active state
                quickNavLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });
}

// Animate rating bars when they come into view
function initRatingAnimations() {
    const ratingBars = document.querySelectorAll('.product-card__rating-fill');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const bar = entry.target;
                const width = bar.style.width;
                
                // Reset and animate
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 100);
                
                observer.unobserve(bar);
            }
        });
    }, {
        threshold: 0.5
    });
    
    ratingBars.forEach(bar => observer.observe(bar));
}

// Product card hover interactions
function initProductCardInteractions() {
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        // Add hover effect for better accessibility
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        // Track card interactions
        card.addEventListener('click', function(e) {
            // Don't track if clicking on links or buttons
            if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON') {
                return;
            }
            
            const productName = this.querySelector('.product-card__title a').textContent;
            trackEvent('product_card_click', {
                product_name: productName,
                card_position: Array.from(productCards).indexOf(this) + 1
            });
        });
    });
}

// Table sorting functionality
function initTableSorting() {
    const table = document.querySelector('.comparison-table__table');
    if (!table) return;
    
    const headers = table.querySelectorAll('th');
    
    headers.forEach((header, index) => {
        // Skip first column (product names) and last column (actions)
        if (index === 0 || index === headers.length - 1) return;
        
        header.style.cursor = 'pointer';
        header.style.userSelect = 'none';
        header.title = 'Click to sort';
        
        // Add sort indicator
        const sortIcon = document.createElement('span');
        sortIcon.innerHTML = ' ↕️';
        sortIcon.style.fontSize = '0.8em';
        header.appendChild(sortIcon);
        
        header.addEventListener('click', function() {
            sortTable(table, index);
            
            // Update sort indicators
            headers.forEach(h => {
                const icon = h.querySelector('span');
                if (icon) icon.innerHTML = ' ↕️';
            });
            
            const currentIcon = this.querySelector('span');
            if (currentIcon) {
                currentIcon.innerHTML = this.dataset.sortDirection === 'desc' ? ' ↑' : ' ↓';
            }
        });
    });
}

function sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const header = table.querySelectorAll('th')[columnIndex];
    
    const isAscending = header.dataset.sortDirection !== 'asc';
    header.dataset.sortDirection = isAscending ? 'asc' : 'desc';
    
    rows.sort((a, b) => {
        const aValue = getCellValue(a, columnIndex);
        const bValue = getCellValue(b, columnIndex);
        
        // Handle numeric values (scores)
        if (!isNaN(aValue) && !isNaN(bValue)) {
            return isAscending ? aValue - bValue : bValue - aValue;
        }
        
        // Handle text values
        return isAscending ? 
            aValue.localeCompare(bValue) : 
            bValue.localeCompare(aValue);
    });
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
    
    // Track sorting action
    trackEvent('table_sort', {
        column: header.textContent.trim(),
        direction: header.dataset.sortDirection
    });
}

function getCellValue(row, columnIndex) {
    const cell = row.cells[columnIndex];
    
    // Extract numeric value from score badges
    const scoreBadge = cell.querySelector('.score-badge');
    if (scoreBadge) {
        return parseFloat(scoreBadge.textContent);
    }
    
    return cell.textContent.trim();
}

// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Analytics tracking
function initAnalytics() {
    // Track page view
    trackEvent('page_view', {
        page_type: 'best_product_list',
        variation: 'v1_modern_cards'
    });
    
    // Track CTA clicks
    const ctaButtons = document.querySelectorAll('.product-card__cta, .btn--primary');
    ctaButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const productCard = this.closest('.product-card, tr');
            const productName = productCard ? 
                productCard.querySelector('.product-card__title a, strong')?.textContent : 
                'Unknown';
            
            trackEvent('cta_click', {
                product_name: productName,
                button_text: this.textContent.trim(),
                button_type: this.classList.contains('product-card__cta') ? 'primary' : 'table'
            });
        });
    });
    
    // Track secondary CTA clicks
    const secondaryButtons = document.querySelectorAll('.product-card__secondary-cta');
    secondaryButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const productCard = this.closest('.product-card');
            const productName = productCard ? 
                productCard.querySelector('.product-card__title a').textContent : 
                'Unknown';
            
            trackEvent('secondary_cta_click', {
                product_name: productName,
                button_text: this.textContent.trim()
            });
        });
    });
    
    // Track scroll depth
    let maxScrollDepth = 0;
    window.addEventListener('scroll', throttle(() => {
        const scrollDepth = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        if (scrollDepth > maxScrollDepth) {
            maxScrollDepth = scrollDepth;
            
            // Track at 25%, 50%, 75%, and 100%
            if (maxScrollDepth >= 25 && maxScrollDepth < 50) {
                trackEvent('scroll_depth', { depth: '25%' });
            } else if (maxScrollDepth >= 50 && maxScrollDepth < 75) {
                trackEvent('scroll_depth', { depth: '50%' });
            } else if (maxScrollDepth >= 75 && maxScrollDepth < 100) {
                trackEvent('scroll_depth', { depth: '75%' });
            } else if (maxScrollDepth >= 100) {
                trackEvent('scroll_depth', { depth: '100%' });
            }
        }
    }, 1000));
}

// Utility function for tracking events
function trackEvent(eventName, properties = {}) {
    // In a real implementation, this would send to your analytics service
    console.log('Analytics Event:', eventName, properties);
    
    // Example: Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, properties);
    }
    
    // Example: Custom analytics
    if (typeof analytics !== 'undefined') {
        analytics.track(eventName, properties);
    }
}

// Throttle function for performance
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Accessibility improvements
function initAccessibility() {
    // Add keyboard navigation for product cards
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach(card => {
        card.setAttribute('tabindex', '0');
        card.setAttribute('role', 'article');
        
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const primaryLink = this.querySelector('.product-card__title a');
                if (primaryLink) {
                    primaryLink.click();
                }
            }
        });
    });
    
    // Improve table accessibility
    const table = document.querySelector('.comparison-table__table');
    if (table) {
        table.setAttribute('role', 'table');
        table.setAttribute('aria-label', 'Product comparison table');
        
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            header.setAttribute('scope', 'col');
            header.setAttribute('id', `header-${index}`);
        });
        
        const cells = table.querySelectorAll('td');
        cells.forEach((cell, index) => {
            const headerIndex = index % headers.length;
            cell.setAttribute('headers', `header-${headerIndex}`);
        });
    }
}

// Initialize accessibility features
document.addEventListener('DOMContentLoaded', initAccessibility);

// Performance monitoring
function initPerformanceMonitoring() {
    // Monitor Core Web Vitals
    if ('web-vital' in window) {
        import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
            getCLS(console.log);
            getFID(console.log);
            getFCP(console.log);
            getLCP(console.log);
            getTTFB(console.log);
        });
    }
    
    // Monitor page load time
    window.addEventListener('load', () => {
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        trackEvent('page_load_time', { load_time: loadTime });
    });
}

// Initialize performance monitoring
initPerformanceMonitoring();
